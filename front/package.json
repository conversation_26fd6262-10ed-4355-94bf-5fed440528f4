{"scripts": {"dev:all": "concurrently --kill-others \"cd ../types/ && npm run start\" \"sleep 20 && cd ../sdks/js/ && npm run start\" \"sleep 22 && next dev\" \"sleep 22 && tsx ./start_worker.ts\"", "dev": "next dev", "dev-datadog": "NODE_OPTIONS='-r dd-trace/init' DD_TAGS=service:front-edge DD_TAGS=env:dev-ben DD_GIT_COMMIT_SHA=`git rev-parse HEAD` DD_GIT_REPOSITORY_URL=https://github.com/dust-tt/dust/ npm run dev", "build": "next build", "start": "next start --keepAliveTimeout 5000", "start:worker": "tsx ./start_worker.ts", "lint:test-filenames": "BAD_FILES=$(find pages -type f -name '*.test.ts' | grep -E '/[^/]*(\\[|\\])[^/]*$'); if [ -n \"$BAD_FILES\" ]; then echo \"Error: Found .test.ts files in 'pages' directory with brackets [] in their names (this can break endpoints):\"; echo \"$BAD_FILES\"; exit 1; else echo \"Filename check: OK. No .test.ts files with brackets found in 'pages'.\"; exit 0; fi", "lint": "npm run lint:test-filenames && next lint", "docs": "npx next-swagger-doc-cli swagger.json 2>&1 | tee /dev/stderr | grep -E \"YAML.*Error\" && { echo \"Could not generate swagger because of errors\" && exit 1; } || { npx @redocly/cli@1.25.5 lint --extends recommended-strict --skip-rule operation-operationId --lint-config error public/swagger.json && npm run format; }", "docs:check": "npx @redocly/cli@1.25.5 lint --extends recommended-strict --skip-rule operation-operationId --lint-config error public/swagger.json", "format": "prettier --write .", "format:check": "prettier --check .", "tsc": "tsc", "test": "vitest", "test:ci": "vitest --reporter=junit --outputFile=junit.xml --watch=false", "coverage": "vitest --coverage", "initdb": "./admin/init_db.sh", "create-db-migration": "./create_db_migration_file.sh", "prepare": "cd .. && husky .husky", "debug:profiler": "tsx ./scripts/debug/run_profiler.ts"}, "dependencies": {"@auth0/nextjs-auth0": "^3.5.0", "@dust-tt/client": "file:../sdks/js", "@dust-tt/sparkle": "^0.2.517", "@google-cloud/bigquery": "^7.9.1", "@google-cloud/storage-transfer": "^3.6.0", "@heroicons/react": "^2.0.11", "@hookform/resolvers": "^3.3.4", "@hubspot/api-client": "^12.0.1", "@mendable/firecrawl-js": "^1.24.0", "@modelcontextprotocol/sdk": "^1.11.1", "@notionhq/client": "^2.3.0", "@octokit/core": "^6.1.5", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-navigation-menu": "^1.1.4", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-visually-hidden": "^1.1.2", "@sendgrid/mail": "^8.0.0", "@tailwindcss/container-queries": "^0.1.1", "@tailwindcss/forms": "^0.5.3", "@tanstack/react-table": "^8.13.0", "@temporalio/activity": "^1.9.3", "@temporalio/client": "^1.9.3", "@temporalio/common": "^1.9.3", "@temporalio/worker": "^1.9.3", "@temporalio/workflow": "^1.9.3", "@textea/json-viewer": "^3.1.1", "@tiptap/extension-character-count": "^2.4.0", "@tiptap/extension-link": "^2.11.7", "@tiptap/extension-mention": "^2.1.13", "@tiptap/extension-placeholder": "^2.1.13", "@tiptap/pm": "^2.1.13", "@tiptap/react": "^2.1.13", "@tiptap/starter-kit": "^2.1.13", "@types/cls-hooked": "^4.3.9", "@types/json-schema": "^7.0.15", "@uiw/react-textarea-code-editor": "^3.0.2", "@workos-inc/node": "^7.50.0", "ajv": "^8.17.1", "auth0": "^4.3.1", "blake3": "^2.1.7", "bottleneck": "^2.19.5", "class-variance-authority": "^0.7.0", "cls-hooked": "^4.2.2", "cmdk": "^1.0.0", "convertapi": "^1.15.0", "cron-parser": "^4.9.0", "csv-parse": "^5.5.2", "csv-stringify": "^6.4.5", "date-fns": "^3.6.0", "dd-trace": "^5.52.0", "diff": "^7.0.0", "embla-carousel-react": "^8.0.1", "eventsource-parser": "^1.0.0", "fast-diff": "^1.3.0", "formidable": "^3.5.1", "fp-ts": "^2.16.5", "fs-extra": "^11.1.1", "googleapis": "^118.0.0", "hot-shots": "^10.0.0", "html-escaper": "^3.0.3", "io-ts": "^2.2.20", "io-ts-reporters": "^2.0.1", "io-ts-types": "^0.5.19", "ipaddr.js": "^2.2.0", "iron-session": "^8.0.4", "jsforce": "^3.8.2", "jsonwebtoken": "^9.0.0", "jszip": "^3.10.1", "jwks-rsa": "^3.1.0", "lodash": "^4.17.21", "lru-memoizer": "^2.2.0", "lucide-react": "^0.363.0", "luxon": "^3.4.4", "marked": "^14.1.3", "minimist": "^1.2.8", "moment-timezone": "^0.5.43", "motion": "^12.7.3", "next": "^14.2.29", "next-swagger-doc": "^0.4.0", "openai": "^4.96.0", "pegjs": "^0.10.0", "pg": "^8.8.0", "pg-hstore": "^2.3.4", "pino": "^8.11.0", "pino-pretty": "^10.0.0", "pkce-challenge": "^4.1.0", "prosemirror-markdown": "^1.13.1", "react": "^18.3.1", "react-beforeunload": "^2.5.3", "react-cookie": "^7.2.2", "react-dom": "^18.3.1", "react-dropzone": "^14.2.3", "react-hook-form": "^7.51.1", "react-image-crop": "^10.1.8", "react-intersection-observer": "^9.13.1", "react-markdown": "^8.0.7", "react-multi-select-component": "^4.3.4", "react-textarea-autosize": "^8.4.0", "redis": "^4.6.8", "sanitize-html": "^2.13.0", "sequelize": "^6.31.0", "sharp": "^0.33.5", "showdown": "^2.1.0", "sqids": "^0.3.0", "sqlite3": "^5.1.6", "stripe": "^14.2.0", "swr": "^2.2.4", "tailwind-merge": "^2.2.1", "tailwind-scrollbar-hide": "^1.1.7", "tailwindcss": "^3.2.4", "tailwindcss-animate": "^1.0.7", "three": "^0.163.0", "tsconfig-paths-webpack-plugin": "^4.1.0", "undici": "^7.8.0", "uuid": "^9.0.0", "yargs": "^17.7.2", "zod": "^3.23.8", "zod-to-json-schema": "^3.24.5", "zod-validation-error": "^3.4.0"}, "devDependencies": {"@faker-js/faker": "^9.3.0", "@google-cloud/storage": "^7.11.2", "@redocly/openapi-cli": "^1.0.0-beta.95", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@testing-library/user-event": "^14.5.2", "@types/diff": "^7.0.2", "@types/express": "^5.0.0", "@types/formidable": "^3.4.3", "@types/fs-extra": "^11.0.1", "@types/html-escaper": "^3.0.4", "@types/jsonwebtoken": "^9.0.2", "@types/lodash": "^4.14.202", "@types/luxon": "^3.4.2", "@types/minimist": "^1.2.2", "@types/node": "^20.17.12", "@types/pegjs": "^0.10.3", "@types/pg": "^8.11.11", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@types/sanitize-html": "^2.11.0", "@types/showdown": "^2.0.1", "@types/three": "^0.154.0", "@types/uuid": "^9.0.1", "@types/yargs": "^17.0.33", "@typescript-eslint/eslint-plugin": "^7.9.0", "@typescript-eslint/parser": "^7.9.0", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.14", "concurrently": "^9.0.1", "danger": "^13.0.4", "eslint": "^8.56.0", "eslint-config-next": "^14.2.3", "eslint-config-prettier": "^9.1.0", "eslint-plugin-dust": "file:../eslint-plugin-dust", "eslint-plugin-jsdoc": "^48.4.0", "eslint-plugin-simple-import-sort": "^12.1.0", "eslint-plugin-unused-imports": "^3.2.0", "husky": "^9.0.11", "jsdom": "^26.0.0", "node-fetch": "^2.7.0", "node-mocks-http": "^1.16.2", "openapi-cli": "^0.0.2", "prettier": "^3.0", "prettier-plugin-tailwindcss": "^0.6.5", "swagger-cli": "^4.0.4", "tsx": "^4.10.2", "typescript": "5.4.5", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.0.8", "vitest-canvas-mock": "^0.3.3"}, "peerDependencies": {"typescript": "5.4.5"}, "browser": {"net": false, "tls": false, "fs": false, "dgram": false, "child_process": false}}